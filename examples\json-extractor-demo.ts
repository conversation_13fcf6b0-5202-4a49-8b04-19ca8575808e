import { SimpleJsonExtractor } from '../nodes/utils/SimpleJsonExtractor';

/**
 * JSON 提取器演示脚本
 * 展示各种实际使用场景
 */

console.log('🚀 JSON 提取器演示开始...\n');

// 场景 1: ChatGPT 风格的输出
console.log('📝 场景 1: ChatGPT 风格输出');
const chatGPTOutput = `
根据您的需求，我为您生成了以下 API 配置：

\`\`\`json
{
  "api": {
    "baseUrl": "https://api.example.com",
    "version": "v1",
    "endpoints": {
      "users": "/users",
      "posts": "/posts",
      "comments": "/comments"
    },
    "authentication": {
      "type": "bearer",
      "tokenHeader": "Authorization"
    },
    "rateLimit": {
      "requests": 1000,
      "window": "1h"
    }
  }
}
\`\`\`

这个配置包含了 API 的基本信息、端点定义和认证设置。您可以根据实际需要调整这些参数。
`;

const result1 = SimpleJsonExtractor.extractJson(chatGPTOutput);
if (result1.success) {
    console.log('✅ 成功提取 API 配置');
    console.log('🔧 基础 URL:', result1.data.api.baseUrl);
    console.log('🔑 认证类型:', result1.data.api.authentication.type);
    console.log('⚡ 速率限制:', `${result1.data.api.rateLimit.requests} 请求/${result1.data.api.rateLimit.window}`);
} else {
    console.log('❌ 提取失败:', result1.error);
}

console.log('\n' + '─'.repeat(80) + '\n');

// 场景 2: 代码生成场景
console.log('📝 场景 2: 代码生成场景');
const codeGenOutput = `
// 这是为您生成的数据库配置
const dbConfig = {
  host: 'localhost',
  port: 5432,
  database: 'myapp',
  username: 'admin',
  password: 'secret123',
  ssl: false,
  pool: {
    min: 2,
    max: 10,
    idle: 30000
  }
};

// 请将此配置保存到您的环境变量中
`;

const result2 = SimpleJsonExtractor.extractJson(codeGenOutput);
if (result2.success) {
    console.log('✅ 成功提取数据库配置');
    console.log('🗄️ 数据库:', `${result2.data.username}@${result2.data.host}:${result2.data.port}/${result2.data.database}`);
    console.log('🏊 连接池:', `${result2.data.pool.min}-${result2.data.pool.max} 连接`);
} else {
    console.log('❌ 提取失败:', result2.error);
}

console.log('\n' + '─'.repeat(80) + '\n');

// 场景 3: 不标准格式修复
console.log('📝 场景 3: 不标准格式自动修复');
const messyJson = `
这里有一个配置，但格式不太标准：

{
  name: '用户管理系统',  // 项目名称
  version: '2.1.0',
  features: [
    'user-auth',
    'role-management',
    'audit-log',  // 尾随逗号
  ],
  settings: {
    theme: 'dark',
    language: 'zh-CN',
    notifications: true,
  },  // 另一个尾随逗号
}

请帮我处理这个配置。
`;

const result3 = SimpleJsonExtractor.extractJson(messyJson);
if (result3.success) {
    console.log('✅ 成功修复并提取配置');
    console.log('📦 项目:', result3.data.name, 'v' + result3.data.version);
    console.log('🎨 主题:', result3.data.settings.theme);
    console.log('🌐 语言:', result3.data.settings.language);
    console.log('🔧 功能:', result3.data.features.join(', '));
    console.log('🛠️ 修复方法:', result3.source);
} else {
    console.log('❌ 提取失败:', result3.error);
}

console.log('\n' + '─'.repeat(80) + '\n');

// 场景 4: 批量提取多个 JSON
console.log('📝 场景 4: 批量提取多个配置');
const multipleJsonText = `
我为您准备了三个不同环境的配置：

开发环境：
{"env": "development", "debug": true, "apiUrl": "http://localhost:3000"}

测试环境：
{"env": "testing", "debug": false, "apiUrl": "https://test-api.example.com"}

生产环境：
{"env": "production", "debug": false, "apiUrl": "https://api.example.com", "ssl": true}

请根据需要选择合适的配置。
`;

const results4 = SimpleJsonExtractor.extractMultipleJson(multipleJsonText);
console.log(`✅ 找到 ${results4.length} 个环境配置:`);
results4.forEach((result, index) => {
    if (result.success) {
        const config = result.data;
        console.log(`  ${index + 1}. ${config.env.toUpperCase()}: ${config.apiUrl} (调试: ${config.debug ? '开启' : '关闭'})`);
    }
});

console.log('\n' + '─'.repeat(80) + '\n');

// 场景 5: 复杂嵌套结构
console.log('📝 场景 5: 复杂嵌套结构');
const complexJson = `
\`\`\`javascript
{
  "workflow": {
    "name": "数据处理流水线",
    "version": "1.0",
    "steps": [
      {
        "id": "extract",
        "name": "数据提取",
        "type": "source",
        "config": {
          "source": "database",
          "query": "SELECT * FROM users WHERE active = true",
          "batchSize": 1000
        }
      },
      {
        "id": "transform",
        "name": "数据转换", 
        "type": "processor",
        "config": {
          "rules": [
            {"field": "email", "action": "lowercase"},
            {"field": "phone", "action": "format", "pattern": "+86-xxx-xxxx-xxxx"},
            {"field": "created_at", "action": "date_format", "format": "YYYY-MM-DD"}
          ]
        }
      },
      {
        "id": "load",
        "name": "数据加载",
        "type": "destination", 
        "config": {
          "target": "elasticsearch",
          "index": "users",
          "bulkSize": 500
        }
      }
    ],
    "schedule": {
      "type": "cron",
      "expression": "0 2 * * *",
      "timezone": "Asia/Shanghai"
    }
  }
}
\`\`\`
`;

const result5 = SimpleJsonExtractor.extractJson(complexJson);
if (result5.success) {
    const workflow = result5.data.workflow;
    console.log('✅ 成功提取工作流配置');
    console.log('📋 工作流:', workflow.name, 'v' + workflow.version);
    console.log('🔄 步骤数量:', workflow.steps.length);
    console.log('⏰ 调度:', workflow.schedule.expression, `(${workflow.schedule.timezone})`);
    
    console.log('📝 处理步骤:');
    workflow.steps.forEach((step: any, index: number) => {
        console.log(`  ${index + 1}. ${step.name} (${step.type})`);
    });
} else {
    console.log('❌ 提取失败:', result5.error);
}

console.log('\n' + '─'.repeat(80) + '\n');

// 场景 6: 错误处理演示
console.log('📝 场景 6: 错误处理演示');
const invalidTexts = [
    '这段文字中没有任何 JSON 数据',
    '{"invalid": json, missing quotes}',
    '这里有一个不完整的 JSON: {"name": "test"',
    ''
];

invalidTexts.forEach((text, index) => {
    const result = SimpleJsonExtractor.extractJson(text);
    console.log(`测试 ${index + 1}: ${result.success ? '✅ 成功' : '❌ 失败'} - ${result.error || '无错误'}`);
});

console.log('\n' + '─'.repeat(80) + '\n');

// 场景 7: 性能测试
console.log('📝 场景 7: 性能测试');
const largeText = `
这是一个包含大量文本的示例，用于测试性能。
${'Lorem ipsum dolor sit amet, consectetur adipiscing elit. '.repeat(100)}

在这大量文本中隐藏着一个 JSON 配置：
{"performance": {"test": true, "iterations": 1000, "memory": "optimized"}}

${'Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. '.repeat(100)}
`;

const startTime = Date.now();
const result7 = SimpleJsonExtractor.extractJson(largeText);
const endTime = Date.now();

if (result7.success) {
    console.log('✅ 性能测试通过');
    console.log('⏱️ 处理时间:', `${endTime - startTime}ms`);
    console.log('📊 提取数据:', JSON.stringify(result7.data));
    console.log('📏 文本长度:', largeText.length, '字符');
} else {
    console.log('❌ 性能测试失败:', result7.error);
}

console.log('\n🎉 演示完成！');

// 导出演示函数供其他地方使用
export function runJsonExtractionDemo() {
    console.log('运行 JSON 提取演示...');
    // 这里可以添加更多演示逻辑
}

// 如果直接运行此文件
if (require.main === module) {
    console.log('直接运行演示脚本');
}
