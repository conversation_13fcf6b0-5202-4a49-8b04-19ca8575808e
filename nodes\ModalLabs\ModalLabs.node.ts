import {
	IExecuteFunctions,
	INodeExecutionData,
	INodeType,
	INodeTypeDescription,
	NodeOperationError,
} from 'n8n-workflow';

import { Function_, App } from 'modal';

export class <PERSON>dal<PERSON>abs implements INodeType {
	description: INodeTypeDescription = {
		displayName: 'Modal Labs',
		name: 'modalLabs',
		icon: 'file:modallabs.svg',
		group: ['transform'],
		version: 1,
		subtitle: '={{$parameter["operation"] + ": " + $parameter["resource"]}}',
		description: 'Interact with Modal Labs cloud platform',
		defaults: {
			name: 'Modal Labs',
			color: '#6366f1',
		},
		inputs: ['main'],
		outputs: ['main'],
		credentials: [
			{
				name: 'modalLabsApi',
				required: true,
			},
		],
		properties: [
			{
				displayName: 'Resource',
				name: 'resource',
				type: 'options',
				noDataExpression: true,
				options: [
					{
						name: 'Function',
						value: 'function',
					},
					{
						name: 'Sandbox',
						value: 'sandbox',
					},
				],
				default: 'function',
			},
			{
				displayName: 'Operation',
				name: 'operation',
				type: 'options',
				noDataExpression: true,
				displayOptions: {
					show: {
						resource: ['function'],
					},
				},
				options: [
					{
						name: 'Call',
						value: 'call',
						description: 'Call a deployed Modal function',
						action: 'Call a function',
					},
					{
						name: 'Spawn',
						value: 'spawn',
						description: 'Spawn a deployed Modal function asynchronously',
						action: 'Spawn a function',
					},
				],
				default: 'call',
			},
			{
				displayName: 'Operation',
				name: 'operation',
				type: 'options',
				noDataExpression: true,
				displayOptions: {
					show: {
						resource: ['sandbox'],
					},
				},
				options: [
					{
						name: 'Create',
						value: 'create',
						description: 'Create a new sandbox',
						action: 'Create a sandbox',
					},
					{
						name: 'Execute',
						value: 'execute',
						description: 'Execute a command in a sandbox',
						action: 'Execute command in sandbox',
					},
					{
						name: 'Get Status',
						value: 'getStatus',
						description: 'Get sandbox status',
						action: 'Get sandbox status',
					},
					{
						name: 'Terminate',
						value: 'terminate',
						description: 'Terminate a sandbox',
						action: 'Terminate a sandbox',
					},
				],
				default: 'create',
			},
			// Function parameters
			{
				displayName: 'App Name',
				name: 'appName',
				type: 'string',
				required: true,
				displayOptions: {
					show: {
						resource: ['function'],
						operation: ['call', 'spawn'],
					},
				},
				default: '',
				placeholder: 'my-app',
				description: 'Name of the deployed Modal app',
			},
			{
				displayName: 'Function Name',
				name: 'functionName',
				type: 'string',
				required: true,
				displayOptions: {
					show: {
						resource: ['function'],
						operation: ['call', 'spawn'],
					},
				},
				default: '',
				placeholder: 'my-function',
				description: 'Name of the deployed Modal function',
			},
			{
				displayName: 'Function Arguments',
				name: 'functionArgs',
				type: 'json',
				displayOptions: {
					show: {
						resource: ['function'],
						operation: ['call', 'spawn'],
					},
				},
				default: '{}',
				description: 'Arguments to pass to the function as JSON',
			},
			// Sandbox parameters
			{
				displayName: 'App Name',
				name: 'appName',
				type: 'string',
				displayOptions: {
					show: {
						resource: ['sandbox'],
						operation: ['create'],
					},
				},
				default: 'libmodal-example',
				description: 'Name of the Modal app (will be created if it doesn\'t exist)',
			},
			{
				displayName: 'Image',
				name: 'image',
				type: 'string',
				displayOptions: {
					show: {
						resource: ['sandbox'],
						operation: ['create'],
					},
				},
				default: 'python:3.11',
				description: 'Docker image to use for the sandbox',
			},
			{
				displayName: 'Sandbox ID',
				name: 'sandboxId',
				type: 'string',
				required: true,
				displayOptions: {
					show: {
						resource: ['sandbox'],
						operation: ['execute', 'getStatus', 'terminate'],
					},
				},
				default: '',
				description: 'ID of the sandbox',
			},
			{
				displayName: 'Command',
				name: 'command',
				type: 'string',
				required: true,
				displayOptions: {
					show: {
						resource: ['sandbox'],
						operation: ['execute'],
					},
				},
				default: '',
				placeholder: 'python -c "print(\'Hello World\')"',
				description: 'Command to execute in the sandbox',
			},
			{
				displayName: 'Working Directory',
				name: 'workingDirectory',
				type: 'string',
				displayOptions: {
					show: {
						resource: ['sandbox'],
						operation: ['execute'],
					},
				},
				default: '/root',
				description: 'Working directory for command execution',
			},
			{
				displayName: 'Environment Variables',
				name: 'environmentVariables',
				type: 'fixedCollection',
				typeOptions: {
					multipleValues: true,
				},
				displayOptions: {
					show: {
						resource: ['sandbox'],
						operation: ['create', 'execute'],
					},
				},
				default: {},
				options: [
					{
						name: 'variable',
						displayName: 'Variable',
						values: [
							{
								displayName: 'Name',
								name: 'name',
								type: 'string',
								default: '',
							},
							{
								displayName: 'Value',
								name: 'value',
								type: 'string',
								default: '',
							},
						],
					},
				],
			},
			{
				displayName: 'Timeout (seconds)',
				name: 'timeout',
				type: 'number',
				displayOptions: {
					show: {
						resource: ['sandbox'],
						operation: ['execute'],
					},
				},
				default: 300,
				description: 'Timeout for command execution in seconds',
			},
		],
	};

	async execute(this: IExecuteFunctions): Promise<INodeExecutionData[][]> {
		const items = this.getInputData();
		const returnData: INodeExecutionData[] = [];

		const credentials = await this.getCredentials('modalLabsApi');

		// Set Modal credentials as environment variables
		process.env.MODAL_TOKEN_ID = credentials.tokenId as string;
		process.env.MODAL_TOKEN_SECRET = credentials.tokenSecret as string;

		for (let i = 0; i < items.length; i++) {
			try {
				const resource = this.getNodeParameter('resource', i) as string;
				const operation = this.getNodeParameter('operation', i) as string;

				let responseData: any;

				if (resource === 'function') {
					if (operation === 'call') {
						const functionName = this.getNodeParameter('functionName', i) as string;
						const appName = this.getNodeParameter('appName', i) as string;
						const functionArgs = this.getNodeParameter('functionArgs', i, '{}') as string;

						let args: any = {};
						try {
							args = JSON.parse(functionArgs);
						} catch (error) {
							throw new NodeOperationError(this.getNode(), `Invalid JSON in function arguments: ${error}`);
						}

						const func = await Function_.lookup(appName, functionName);
						responseData = await func.remote([], args);

					} else if (operation === 'spawn') {
						const functionName = this.getNodeParameter('functionName', i) as string;
						const appName = this.getNodeParameter('appName', i) as string;
						const functionArgs = this.getNodeParameter('functionArgs', i, '{}') as string;

						let args: any = {};
						try {
							args = JSON.parse(functionArgs);
						} catch (error) {
							throw new NodeOperationError(this.getNode(), `Invalid JSON in function arguments: ${error}`);
						}

						const func = await Function_.lookup(appName, functionName);
						const execution = await func.spawn([], args);
						responseData = {
							execution: execution,
							status: 'spawned',
							appName,
							functionName,
						};
					}
				} else if (resource === 'sandbox') {
					if (operation === 'create') {
						const appName = this.getNodeParameter('appName', i, 'libmodal-example') as string;
						const image = this.getNodeParameter('image', i) as string;
						const environmentVariables = this.getNodeParameter('environmentVariables', i, {}) as any;

						const env: Record<string, string> = {};
						if (environmentVariables.variable) {
							for (const variable of environmentVariables.variable) {
								env[variable.name] = variable.value;
							}
						}

						const app = await App.lookup(appName, { createIfMissing: true });
						const modalImage = await app.imageFromRegistry(image);
						const sandbox = await app.createSandbox(modalImage);

						responseData = {
							sandboxId: sandbox.sandboxId,
							appName,
							image,
						};

					} else if (operation === 'execute') {
						// For sandbox execution, we need to get the sandbox object
						// This is a simplified implementation - in practice you'd need to store sandbox references
						throw new NodeOperationError(this.getNode(), 'Sandbox execution is not yet implemented. Please use the create operation to get a sandbox ID.');

					} else if (operation === 'getStatus') {
						// This would require storing sandbox references
						throw new NodeOperationError(this.getNode(), 'Sandbox status check is not yet implemented.');

					} else if (operation === 'terminate') {
						// This would require storing sandbox references
						throw new NodeOperationError(this.getNode(), 'Sandbox termination is not yet implemented.');
					}
				}

				returnData.push({
					json: responseData,
					pairedItem: {
						item: i,
					},
				});

			} catch (error) {
				if (this.continueOnFail()) {
					returnData.push({
						json: {
							error: error instanceof Error ? error.message : String(error),
						},
						pairedItem: {
							item: i,
						},
					});
					continue;
				}
				throw error;
			}
		}

		return [returnData];
	}
}
