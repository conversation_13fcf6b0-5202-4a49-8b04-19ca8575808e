# Modal Labs n8n 节点设置指南

## 概述

我已经成功为您创建了一个 Modal Labs n8n 节点，允许您在 n8n 工作流中与 Modal Labs 云平台进行交互。

## 已创建的文件

### 节点文件
- `nodes/ModalLabs/ModalLabs.node.ts` - 主要节点实现
- `nodes/ModalLabs/modallabs.svg` - 节点图标
- `credentials/ModalLabsApi.credentials.ts` - API 凭据配置

### 文档
- `docs/ModalLabs.md` - 详细使用文档
- `examples/modal-labs-example.json` - 示例工作流

### 配置更新
- `package.json` - 添加了 modal 依赖和节点配置
- `nodes/index.ts` - 导出新节点
- `credentials/index.ts` - 导出凭据配置
- `tsconfig.json` - 包含 credentials 目录

## 功能特性

### 支持的操作

#### 函数操作
1. **Call** - 同步调用已部署的 Modal 函数
2. **Spawn** - 异步启动 Modal 函数

#### 沙箱操作
1. **Create** - 创建新的沙箱环境

### 参数配置

#### 函数调用
- **App Name**: Modal 应用名称
- **Function Name**: 函数名称
- **Function Arguments**: JSON 格式的函数参数

#### 沙箱创建
- **App Name**: Modal 应用名称
- **Image**: Docker 镜像名称
- **Environment Variables**: 环境变量配置

## 使用步骤

### 1. 配置凭据
1. 在 n8n 中创建 "Modal Labs API" 凭据
2. 输入您的 Modal Labs Token ID (ak-xxx) 和 Token Secret (as-xxx)

### 2. 使用节点
1. 在工作流中添加 "Modal Labs" 节点
2. 选择资源类型（Function 或 Sandbox）
3. 选择操作类型
4. 配置相应参数

### 3. 示例用法

#### 调用函数
```json
{
  "resource": "function",
  "operation": "call",
  "appName": "my-app",
  "functionName": "echo_string",
  "functionArgs": "{\"text\": \"Hello World\"}"
}
```

#### 创建沙箱
```json
{
  "resource": "sandbox",
  "operation": "create",
  "appName": "libmodal-example",
  "image": "python:3.11"
}
```

## 技术实现

### 依赖项
- `modal`: Modal Labs JavaScript SDK (v0.3.15)
- `@types/node`: Node.js 类型定义

### 认证方式
节点通过环境变量设置 Modal Labs 凭据：
- `MODAL_TOKEN_ID`
- `MODAL_TOKEN_SECRET`

### API 调用
- 使用 `Function_.lookup()` 查找和调用函数
- 使用 `App.lookup()` 和 `app.createSandbox()` 创建沙箱

## 限制和注意事项

1. **沙箱操作限制**: 目前只支持创建沙箱，执行、状态查询和终止操作需要进一步实现
2. **函数参数**: 只支持 JSON 格式的参数
3. **错误处理**: 包含基本的错误处理和继续执行选项
4. **认证**: 需要有效的 Modal Labs API 令牌

## 构建状态

✅ 节点代码已完成
✅ 凭据配置已完成
✅ TypeScript 编译成功
✅ 文档已创建
✅ 示例已提供

## 下一步

1. 在 n8n 中测试节点功能
2. 根据需要扩展沙箱操作功能
3. 添加更多错误处理和验证
4. 优化用户体验和文档

## 支持

如果您在使用过程中遇到问题，请参考：
- [Modal Labs 官方文档](https://modal.com/docs)
- [Modal Labs JavaScript SDK](https://github.com/modal-labs/libmodal)
- 本项目的 `docs/ModalLabs.md` 文档
