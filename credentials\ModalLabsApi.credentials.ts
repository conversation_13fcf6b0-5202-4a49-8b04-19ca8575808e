import {
	IAuthenticateGeneric,
	ICredentialTestRequest,
	ICredentialType,
	INodeProperties,
} from 'n8n-workflow';

export class ModalLabsApi implements ICredentialType {
	name = 'modalLabsApi';
	displayName = 'Modal Labs API';
	documentationUrl = 'https://modal.com/docs';
	properties: INodeProperties[] = [
		{
			displayName: 'Token ID',
			name: 'tokenId',
			type: 'string',
			default: '',
			required: true,
			description: 'Your Modal Labs Token ID (starts with ak-)',
		},
		{
			displayName: 'Token Secret',
			name: 'tokenSecret',
			type: 'string',
			typeOptions: {
				password: true,
			},
			default: '',
			required: true,
			description: 'Your Modal Labs Token Secret (starts with as-)',
		},
	];

	authenticate: IAuthenticateGeneric = {
		type: 'generic',
		properties: {
			headers: {
				Authorization: '=Bearer {{$credentials.tokenId}}:{{$credentials.tokenSecret}}',
			},
		},
	};

	test: ICredentialTestRequest = {
		request: {
			baseURL: 'https://api.modal.com',
			url: '/v1/apps',
			method: 'GET',
		},
	};
}
