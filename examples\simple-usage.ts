/**
 * 简单使用示例
 * 展示如何在实际项目中使用 JSON 提取器
 */

import { SimpleJsonExtractor } from '../nodes/utils/SimpleJsonExtractor';

// 示例 1: 基本使用
console.log('=== 示例 1: 基本使用 ===');

const basicExample = '{"name": "张三", "age": 30, "city": "北京"}';
const result1 = SimpleJsonExtractor.extractJson(basicExample);

if (result1.success) {
    console.log('✅ 提取成功:', result1.data);
} else {
    console.log('❌ 提取失败:', result1.error);
}

// 示例 2: Markdown 格式
console.log('\n=== 示例 2: Markdown 格式 ===');

const markdownExample = `
用户信息如下：
\`\`\`json
{
  "user": {
    "name": "李四",
    "email": "<EMAIL>",
    "role": "admin"
  }
}
\`\`\`
请处理这个用户数据。
`;

const result2 = SimpleJsonExtractor.extractJson(markdownExample);
if (result2.success) {
    console.log('✅ 从 Markdown 提取成功:', result2.data.user);
} else {
    console.log('❌ 提取失败:', result2.error);
}

// 示例 3: 不标准格式自动修复
console.log('\n=== 示例 3: 自动修复不标准格式 ===');

const messyExample = `{
  name: '王五',  // 姓名
  age: 25,
  hobbies: ['reading', 'coding',],  // 尾随逗号
}`;

const result3 = SimpleJsonExtractor.extractJson(messyExample);
if (result3.success) {
    console.log('✅ 修复并提取成功:', result3.data);
    console.log('🔧 修复方法:', result3.source);
} else {
    console.log('❌ 修复失败:', result3.error);
}

// 示例 4: 批量提取
console.log('\n=== 示例 4: 批量提取 ===');

const multipleExample = `
配置文件包含多个环境：
开发环境: {"env": "dev", "debug": true}
生产环境: {"env": "prod", "debug": false}
测试数据: [1, 2, 3, 4, 5]
`;

const results4 = SimpleJsonExtractor.extractMultipleJson(multipleExample);
console.log(`✅ 找到 ${results4.length} 个 JSON 对象:`);
results4.forEach((result, index) => {
    if (result.success) {
        console.log(`  ${index + 1}. ${JSON.stringify(result.data)}`);
    }
});

// 示例 5: 错误处理
console.log('\n=== 示例 5: 错误处理 ===');

const invalidExample = '这里没有任何 JSON 数据';
const result5 = SimpleJsonExtractor.extractJson(invalidExample);

if (!result5.success) {
    console.log('❌ 预期的失败:', result5.error);
    console.log('🔍 可以检查是否包含 JSON:', SimpleJsonExtractor.containsJson(invalidExample));
}

// 示例 6: 实际应用场景 - 处理 ChatGPT 响应
console.log('\n=== 示例 6: 实际应用场景 ===');

function processChatGPTResponse(response: string) {
    console.log('处理 ChatGPT 响应...');
    
    const result = SimpleJsonExtractor.extractJson(response);
    
    if (result.success) {
        console.log('✅ 成功提取配置数据');
        return result.data;
    } else {
        console.log('⚠️ 未找到 JSON 数据，返回原始文本');
        return { originalText: response, error: result.error };
    }
}

const chatGPTResponse = `
根据您的需求，我为您生成了以下数据库配置：

\`\`\`json
{
  "database": {
    "host": "localhost",
    "port": 5432,
    "name": "myapp",
    "ssl": true
  },
  "pool": {
    "min": 2,
    "max": 10
  }
}
\`\`\`

这个配置适用于生产环境，请根据实际情况调整参数。
`;

const config = processChatGPTResponse(chatGPTResponse);
console.log('📊 提取的配置:', JSON.stringify(config, null, 2));

// 示例 7: 类型安全使用
console.log('\n=== 示例 7: 类型安全使用 ===');

interface UserConfig {
    name: string;
    email: string;
    settings: {
        theme: string;
        language: string;
    };
}

function extractUserConfig(text: string): UserConfig | null {
    const result = SimpleJsonExtractor.extractJson(text);
    
    if (!result.success) {
        return null;
    }
    
    const data = result.data as UserConfig;
    
    // 验证数据结构
    if (data.name && data.email && data.settings) {
        return data;
    }
    
    return null;
}

const userConfigText = `{
  "name": "赵六",
  "email": "<EMAIL>",
  "settings": {
    "theme": "dark",
    "language": "zh-CN"
  }
}`;

const userConfig = extractUserConfig(userConfigText);
if (userConfig) {
    console.log('✅ 用户配置验证通过:', userConfig.name);
    console.log('🎨 主题设置:', userConfig.settings.theme);
} else {
    console.log('❌ 用户配置验证失败');
}

console.log('\n🎉 所有示例完成！');

// 导出便捷函数
export {
    processChatGPTResponse,
    extractUserConfig
};
