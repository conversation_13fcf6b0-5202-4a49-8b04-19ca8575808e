# JSON 提取器使用指南

## 概述

JSON 提取器是一个强大的工具，专门用于从大模型（LLM）输出中提取 JSON 数据。它支持多种格式，包括纯 JSON、Markdown 包裹的 JSON，以及各种不标准的 JSON 格式。

## 特性

- ✅ **纯 JSON 解析** - 直接解析标准 JSON 格式
- ✅ **Markdown 支持** - 从代码块中提取 JSON
- ✅ **格式修复** - 自动修复常见的 JSON 格式问题
- ✅ **批量提取** - 从文本中提取多个 JSON 对象
- ✅ **错误处理** - 详细的错误信息和降级策略
- ✅ **高兼容性** - 不依赖原生编译，纯 JavaScript 实现

## 安装

```bash
pnpm add web-tree-sitter
```

## 基本使用

### 1. 简单提取

```typescript
import { SimpleJsonExtractor } from './utils/SimpleJsonExtractor';

const text = '{"name": "张三", "age": 30}';
const result = SimpleJsonExtractor.extractJson(text);

if (result.success) {
    console.log('提取成功:', result.data);
    console.log('提取方法:', result.source);
} else {
    console.log('提取失败:', result.error);
}
```

### 2. 从 Markdown 中提取

```typescript
const markdownText = `
这是用户信息：
\`\`\`json
{
  "user": {
    "name": "李四",
    "email": "<EMAIL>"
  }
}
\`\`\`
请处理这个数据。
`;

const result = SimpleJsonExtractor.extractJson(markdownText);
// result.data = { user: { name: "李四", email: "<EMAIL>" } }
// result.source = "markdown-json"
```

### 3. 批量提取

```typescript
const text = `
配置1：{"name": "config1", "value": 100}
配置2：{"name": "config2", "value": 200}
数组：[1, 2, 3, 4, 5]
`;

const results = SimpleJsonExtractor.extractMultipleJson(text);
console.log(`找到 ${results.length} 个 JSON 对象`);
```

## 支持的格式

### 1. 标准 JSON

```json
{"name": "张三", "age": 30, "city": "北京"}
```

### 2. Markdown 代码块

````markdown
```json
{"name": "李四", "email": "<EMAIL>"}
```
````

````markdown
```javascript
{"database": {"host": "localhost", "port": 5432}}
```
````

### 3. 不标准格式（自动修复）

```javascript
// 单引号
{'name': '王五', 'status': 'active'}

// 尾随逗号
{
  "id": 123,
  "name": "示例",
  "active": true,
}

// 带注释
{
  "name": "测试项目", // 项目名称
  "version": "1.0.0"
  /* 版本信息 */
}

// 未引用的键
{
  name: "项目",
  version: "1.0.0"
}
```

## API 参考

### SimpleJsonExtractor.extractJson(text: string)

提取第一个找到的 JSON 对象。

**参数:**
- `text` - 包含 JSON 的文本

**返回值:**
```typescript
interface JsonExtractionResult {
    success: boolean;
    data?: any;
    error?: string;
    source: 'pure-json' | 'markdown-json' | 'regex-extraction' | 'manual-fix';
    originalText?: string;
    extractedText?: string;
}
```

### SimpleJsonExtractor.extractMultipleJson(text: string)

提取所有找到的 JSON 对象。

**返回值:** `JsonExtractionResult[]`

### SimpleJsonExtractor.containsJson(text: string)

检查文本是否包含 JSON。

**返回值:** `boolean`

### SimpleJsonExtractor.formatExtractedJson(result: JsonExtractionResult, indent?: number)

格式化提取的 JSON。

**返回值:** `string`

## 在 n8n 中使用

### 作为工具函数

```typescript
import { extractJsonFromLLMOutput } from './utils/LLMJsonExtractor.node';

// 在 n8n 节点中使用
const llmOutput = this.getInputData()[0].json.text;
const jsonData = await extractJsonFromLLMOutput(llmOutput);
```

### 作为独立节点

LLM JSON Extractor 节点提供了图形化界面：

1. **输入字段** - 指定包含文本的字段名
2. **输出模式** - 选择提取第一个还是所有 JSON
3. **失败处理** - 配置找不到 JSON 时的行为
4. **元数据** - 是否包含提取过程信息

## 错误处理

### 提取方法优先级

1. **pure-json** - 直接解析标准 JSON
2. **markdown-json** - 从 Markdown 代码块提取
3. **regex-extraction** - 使用正则表达式匹配
4. **manual-fix** - 自动修复格式问题

### 常见错误及解决方案

| 错误类型 | 原因 | 解决方案 |
|---------|------|----------|
| `Invalid input` | 输入不是字符串 | 检查输入数据类型 |
| `No JSON found` | 文本中没有 JSON | 检查文本内容 |
| `JSON parse error` | JSON 格式错误 | 启用自动修复功能 |

## 性能优化

1. **缓存结果** - 对相同输入缓存提取结果
2. **限制文本长度** - 对超长文本进行截断
3. **并行处理** - 批量处理时使用并行提取

## 最佳实践

### 1. 错误处理

```typescript
const result = SimpleJsonExtractor.extractJson(text);
if (!result.success) {
    console.error(`JSON 提取失败: ${result.error}`);
    // 降级处理
    return { error: result.error, originalText: text };
}
```

### 2. 类型安全

```typescript
interface ExpectedData {
    name: string;
    age: number;
}

const result = SimpleJsonExtractor.extractJson(text);
if (result.success) {
    const data = result.data as ExpectedData;
    // 验证数据结构
    if (data.name && typeof data.age === 'number') {
        // 安全使用数据
    }
}
```

### 3. 批量处理

```typescript
const texts = ['text1', 'text2', 'text3'];
const results = texts.map(text => 
    SimpleJsonExtractor.extractJson(text)
).filter(result => result.success);
```

## 示例场景

### 1. ChatGPT 输出处理

```typescript
const chatGPTResponse = `
根据您的要求，我生成了以下配置：

\`\`\`json
{
  "database": {
    "host": "localhost",
    "port": 5432,
    "name": "myapp"
  },
  "features": ["auth", "logging", "caching"]
}
\`\`\`

这个配置包含了数据库连接信息。
`;

const config = SimpleJsonExtractor.extractJson(chatGPTResponse);
// 直接使用提取的配置
```

### 2. 代码生成场景

```typescript
const codeGenOutput = `
// 生成的配置对象
const config = {
  name: "项目名称",
  version: "1.0.0",
  dependencies: ["express", "mongoose"]
};
`;

// 提取配置对象
const result = SimpleJsonExtractor.extractJson(codeGenOutput);
```

### 3. 多语言支持

```typescript
// 支持中文字段名
const chineseJson = '{"姓名": "张三", "年龄": 30, "城市": "北京"}';
const result = SimpleJsonExtractor.extractJson(chineseJson);
// result.data = { "姓名": "张三", "年龄": 30, "城市": "北京" }
```

## 故障排除

### 常见问题

1. **Q: 为什么某些 JSON 提取失败？**
   A: 检查 JSON 格式是否正确，尝试使用在线 JSON 验证器。

2. **Q: 如何处理超大文本？**
   A: 考虑分段处理或使用流式解析。

3. **Q: 提取的数据类型不正确怎么办？**
   A: 添加类型验证和转换逻辑。

### 调试技巧

```typescript
const result = SimpleJsonExtractor.extractJson(text);
console.log('提取结果:', {
    success: result.success,
    source: result.source,
    error: result.error,
    extractedText: result.extractedText?.substring(0, 100)
});
```
