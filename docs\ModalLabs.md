# Modal Labs 节点

Modal Labs 节点允许您在 n8n 工作流中与 Modal Labs 云平台进行交互。

## 功能

- 调用已部署的 Modal 函数
- 异步启动 Modal 函数
- 创建沙箱环境（实验性功能）

## 设置

### 1. 获取 Modal Labs 凭据

1. 访问 [Modal Labs](https://modal.com) 并注册账户
2. 在控制台中创建 API 令牌
3. 获取您的 Token ID（以 `ak-` 开头）和 Token Secret（以 `as-` 开头）

### 2. 配置凭据

在 n8n 中：
1. 转到 **凭据** 页面
2. 点击 **添加凭据**
3. 选择 **Modal Labs API**
4. 输入您的 Token ID 和 Token Secret
5. 保存凭据

## 使用方法

### 调用函数

1. 选择 **Resource**: Function
2. 选择 **Operation**: Call
3. 输入 **App Name**: 您部署的 Modal 应用名称
4. 输入 **Function Name**: 要调用的函数名称
5. 输入 **Function Arguments**: JSON 格式的函数参数

示例：
```json
{
  "text": "Hello, Modal!",
  "count": 5
}
```

### 异步启动函数

1. 选择 **Resource**: Function
2. 选择 **Operation**: Spawn
3. 配置与调用函数相同的参数

这将异步启动函数并返回执行信息，而不等待函数完成。

### 创建沙箱

1. 选择 **Resource**: Sandbox
2. 选择 **Operation**: Create
3. 输入 **App Name**: Modal 应用名称（如果不存在会自动创建）
4. 输入 **Image**: Docker 镜像名称（例如：`python:3.11`）
5. 可选：配置环境变量

## 示例工作流

### 文本处理示例

假设您有一个部署在 Modal 上的文本处理函数：

1. **输入数据**：包含要处理的文本
2. **Modal Labs 节点**：
   - Resource: Function
   - Operation: Call
   - App Name: `text-processor`
   - Function Name: `process_text`
   - Function Arguments: `{"text": "{{$json.text}}"}`
3. **输出**：处理后的文本结果

### 批量图像处理

对于需要大量计算资源的图像处理：

1. **输入数据**：图像 URL 列表
2. **Modal Labs 节点**：
   - Resource: Function
   - Operation: Spawn（异步处理）
   - App Name: `image-processor`
   - Function Name: `process_image`
   - Function Arguments: `{"image_url": "{{$json.url}}"}`

## 注意事项

- 确保您的 Modal 函数已正确部署并可访问
- 函数参数必须是有效的 JSON 格式
- 沙箱功能目前处于实验阶段，某些操作可能尚未完全实现
- 异步函数调用（spawn）不会等待函数完成，适用于长时间运行的任务

## 故障排除

### 常见错误

1. **认证失败**：检查您的 Token ID 和 Token Secret 是否正确
2. **函数未找到**：确认 App Name 和 Function Name 是否正确
3. **参数错误**：确保函数参数是有效的 JSON 格式

### 调试提示

- 使用 n8n 的调试模式查看详细的错误信息
- 检查 Modal Labs 控制台中的函数日志
- 确保您的 Modal 函数接受正确的参数格式

## 更多信息

- [Modal Labs 文档](https://modal.com/docs)
- [Modal Labs JavaScript SDK](https://github.com/modal-labs/libmodal)
